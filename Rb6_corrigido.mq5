//+------------------------------------------------------------------+
//|                                                   Rb6_corrigido.mq5 |
//|                                  Copyright 2023, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property strict

#include <Trade\Trade.mqh>

// Parâmetros de entrada
input double lote = 0.01;                // Tamanho do lote
input int inputStopLoss = 50;            // Stop Loss em pips
input int inputTakeProfit = 100;         // Take Profit em pips
input double lucroProtecao = 5.0;        // Lucro para ativar proteção ($)
input double lucroFechamentoAutomatico = 20.0; // Lucro para fechamento automático ($)
input int trailingStart = 20;            // Trailing Start em pips
input int trailingStep = 5;              // Trailing Step em pips
input int maxOrdens = 10;                // Número máximo de ordens
input string webhookURL = "";            // URL do webhook para notificações

CTrade trade;
string prefixoComentario = "RoboSMT5_";  // Prefixo para identificar ordens deste EA

string botaoCompra = "btn_buy";
string botaoVenda = "btn_sell";
string botaoFechar = "btn_close_all";
string botaoFecharLucro = "btn_close_profit";
string textoInfo = "info_text";
string painelFundo = "painel_fundo";

// Variáveis para os objetos de linha
string prefixoLinhaTP = "tp_line_";
string prefixoLinhaSL = "sl_line_";
string prefixoLinhaEntrada = "entry_line_";

// Variáveis de controle para proteção de lucro
struct ProtecaoLucro
{
   ulong ticket;
   bool protegido;
   double lucroQuandoProtegido;
};

ProtecaoLucro posicoes_protegidas[100];
int total_protegidas = 0;

// Variáveis para controle de alertas de lucro
struct AlertaLucro
{
   ulong ticket;
   double ultimoLucroAlertado;
   datetime ultimoAlerta;
};

AlertaLucro alertas_lucro[100];
int total_alertas = 0;

// Variáveis para controle de posições abertas
struct PosicaoAberta
{
   ulong ticket;
   string simbolo;
   datetime timeAbertura;
   double precoAbertura;
   double lote;
   ENUM_POSITION_TYPE tipo;
   double stopLoss;
   double takeProfit;
   string comentario;
   bool notificada;
};

PosicaoAberta posicoes_abertas[100];
int total_posicoes_abertas = 0;

// Contadores de operações
int greens = 0;
int reds = 0;
double lucroTotal = 0.0;

//+------------------------------------------------------------------+
int OnInit()
{
   // Inicializar array de proteção
   for(int i = 0; i < 100; i++)
   {
      posicoes_protegidas[i].ticket = 0;
      posicoes_protegidas[i].protegido = false;
      posicoes_protegidas[i].lucroQuandoProtegido = 0.0;
   }
   total_protegidas = 0;
   
   // Inicializar array de alertas
   for(int i = 0; i < 100; i++)
   {
      alertas_lucro[i].ticket = 0;
      alertas_lucro[i].ultimoLucroAlertado = 0.0;
      alertas_lucro[i].ultimoAlerta = 0;
   }
   total_alertas = 0;
   
   // Inicializar array de posições abertas
   for(int i = 0; i < 100; i++)
   {
      posicoes_abertas[i].ticket = 0;
      posicoes_abertas[i].notificada = false;
   }
   total_posicoes_abertas = 0;
   
   // Resetar contadores de operações
   greens = 0;
   reds = 0;
   lucroTotal = 0.0;
   
   // Habilitar eventos de clique no gráfico
   ChartSetInteger(0, CHART_EVENT_OBJECT_CREATE, true);
   ChartSetInteger(0, CHART_EVENT_OBJECT_DELETE, true);
   ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);
   ChartSetInteger(0, CHART_EVENT_CLICK, true);

   CriarBotoes();
   CriarTextoInfo();
   ChartRedraw();

   Print("=== ROBO SMT5 v6.0 INICIADO ===");
   Print("Configurações:");
   Print("- Lote: ", lote);
   Print("- Stop Loss: ", inputStopLoss, " pips");
   Print("- Take Profit: ", inputTakeProfit, " pips");
   Print("- Proteção de Lucro: $", lucroProtecao);
   Print("- Fechamento Automático: $", lucroFechamentoAutomatico);
   Print("- Trailing Start: ", trailingStart, " pips");
   Print("- Max Ordens: ", maxOrdens);
   Print("- Webhook URL: ", webhookURL);
   Print("========================");

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ObjectDelete(0, botaoCompra);
   ObjectDelete(0, botaoVenda);
   ObjectDelete(0, botaoFechar);
   ObjectDelete(0, botaoFecharLucro);
   ObjectDelete(0, textoInfo);
   
   // Remover todas as linhas de SL, TP e entrada
   RemoverTodasLinhasSLTP();
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
void OnTick()
{
   // Verificar novas posições abertas
   VerificarNovasPosicoes();
   
   // Verificar posições fechadas
   VerificarPosicoesFechadas();
   
   // Verificar proteção de lucro
   VerificarProtecaoLucro();
   
   // Verificar fechamento automático
   VerificarFechamentoAutomatico();
   
   // Verificar trailing stop
   VerificarTrailingStop();
   
   // Limpar posições protegidas que já foram fechadas
   LimparPosicoesProtegidas();
   
   // Atualizar linhas de SL e TP
   AtualizarLinhasSLTP();
   
   // Atualizar texto de informações
   AtualizarTextoInfo();
}

//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // Verificar se é um clique em um objeto
   if(id == CHARTEVENT_OBJECT_CLICK)
   {
      Print("Objeto clicado: ", sparam);
      
      if(sparam == botaoCompra)
      {
         Print("Botão COMPRA clicado");
         // Verificar se já atingiu o número máximo de ordens
         if(ContarOrdens() < maxOrdens)
         {
            Print("Executando compra...");
            ExecutarCompra();
            // Resetar o estado do botão
            ObjectSetInteger(0, botaoCompra, OBJPROP_STATE, false);
         }
         else
         {
            Print("Número máximo de ordens atingido: ", maxOrdens);
            // Resetar o estado do botão
            ObjectSetInteger(0, botaoCompra, OBJPROP_STATE, false);
         }
         ChartRedraw();
      }
      else if(sparam == botaoVenda)
      {
         Print("Botão VENDA clicado");
         // Verificar se já atingiu o número máximo de ordens
         if(ContarOrdens() < maxOrdens)
         {
            Print("Executando venda...");
            ExecutarVenda();
            // Resetar o estado do botão
            ObjectSetInteger(0, botaoVenda, OBJPROP_STATE, false);
         }
         else
         {
            Print("Número máximo de ordens atingido: ", maxOrdens);
            // Resetar o estado do botão
            ObjectSetInteger(0, botaoVenda, OBJPROP_STATE, false);
         }
         ChartRedraw();
      }
      else if(sparam == botaoFechar)
      {
         Print("Botão FECHAR clicado");
         // Fechar todas as posições abertas
         FecharTodasPosicoes();
         // Resetar o estado do botão
         ObjectSetInteger(0, botaoFechar, OBJPROP_STATE, false);
         ChartRedraw();
      }
      else if(sparam == botaoFecharLucro)
      {
         Print("Botão FECHAR LUCRO clicado");
         // Fechar todas as posições com lucro positivo
         FecharPosicoesComLucro();
         // Resetar o estado do botão
         ObjectSetInteger(0, botaoFecharLucro, OBJPROP_STATE, false);
         ChartRedraw();
      }
   }
}

//+------------------------------------------------------------------+
void CriarBotoes()
{
   // Definir tamanhos e posições para botões menores e lado a lado
   int larguraBotao = 60;  // Botões mais estreitos
   int alturaBotao = 20;   // Botões mais baixos
   int espacamento = 5;    // Menor espaçamento entre botões
   int distanciaBase = 40; // Distância da base da tela (mais acima)
   int distanciaLateral = 10; // Distância da lateral esquerda
   
   // Botão COMPRA (primeiro da esquerda)
   ObjectDelete(0, botaoCompra);
   if(ObjectCreate(0, botaoCompra, OBJ_BUTTON, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoCompra, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, botaoCompra, OBJPROP_XDISTANCE, distanciaLateral);
      ObjectSetInteger(0, botaoCompra, OBJPROP_YDISTANCE, distanciaBase);
      ObjectSetInteger(0, botaoCompra, OBJPROP_XSIZE, larguraBotao);
      ObjectSetInteger(0, botaoCompra, OBJPROP_YSIZE, alturaBotao);
      ObjectSetInteger(0, botaoCompra, OBJPROP_BGCOLOR, clrGreen);
      ObjectSetInteger(0, botaoCompra, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoCompra, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoCompra, OBJPROP_TEXT, "COMPRA");
      ObjectSetInteger(0, botaoCompra, OBJPROP_FONTSIZE, 7); // Fonte ainda menor
      ObjectSetInteger(0, botaoCompra, OBJPROP_STATE, false);
      ObjectSetInteger(0, botaoCompra, OBJPROP_SELECTABLE, false);
   }

   // Botão VENDA (segundo da esquerda)
   ObjectDelete(0, botaoVenda);
   if(ObjectCreate(0, botaoVenda, OBJ_BUTTON, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoVenda, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, botaoVenda, OBJPROP_XDISTANCE, distanciaLateral + larguraBotao + espacamento);
      ObjectSetInteger(0, botaoVenda, OBJPROP_YDISTANCE, distanciaBase);
      ObjectSetInteger(0, botaoVenda, OBJPROP_XSIZE, larguraBotao);
      ObjectSetInteger(0, botaoVenda, OBJPROP_YSIZE, alturaBotao);
      ObjectSetInteger(0, botaoVenda, OBJPROP_BGCOLOR, clrRed);
      ObjectSetInteger(0, botaoVenda, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoVenda, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoVenda, OBJPROP_TEXT, "VENDA");
      ObjectSetInteger(0, botaoVenda, OBJPROP_FONTSIZE, 7); // Fonte ainda menor
      ObjectSetInteger(0, botaoVenda, OBJPROP_STATE, false);
      ObjectSetInteger(0, botaoVenda, OBJPROP_SELECTABLE, false);
   }

   // Botão FECHAR (terceiro da esquerda)
   ObjectDelete(0, botaoFechar);
   if(ObjectCreate(0, botaoFechar, OBJ_BUTTON, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoFechar, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, botaoFechar, OBJPROP_XDISTANCE, distanciaLateral + (larguraBotao + espacamento) * 2);
      ObjectSetInteger(0, botaoFechar, OBJPROP_YDISTANCE, distanciaBase);
      ObjectSetInteger(0, botaoFechar, OBJPROP_XSIZE, larguraBotao);
      ObjectSetInteger(0, botaoFechar, OBJPROP_YSIZE, alturaBotao);
      ObjectSetInteger(0, botaoFechar, OBJPROP_BGCOLOR, clrDarkOrange);
      ObjectSetInteger(0, botaoFechar, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoFechar, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoFechar, OBJPROP_TEXT, "FECHAR");
      ObjectSetInteger(0, botaoFechar, OBJPROP_FONTSIZE, 7); // Fonte ainda menor
      ObjectSetInteger(0, botaoFechar, OBJPROP_STATE, false);
      ObjectSetInteger(0, botaoFechar, OBJPROP_SELECTABLE, false);
   }
   
   // Botão FECHAR LUCRO (quarto da esquerda)
   ObjectDelete(0, botaoFecharLucro);
   if(ObjectCreate(0, botaoFecharLucro, OBJ_BUTTON, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_XDISTANCE, distanciaLateral + (larguraBotao + espacamento) * 3);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_YDISTANCE, distanciaBase);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_XSIZE, larguraBotao);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_YSIZE, alturaBotao);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_BGCOLOR, clrDodgerBlue);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoFecharLucro, OBJPROP_TEXT, "LUCRO");
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_FONTSIZE, 7); // Fonte ainda menor
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_STATE, false);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_SELECTABLE, false);
   }
}

//+------------------------------------------------------------------+
void CriarTextoInfo()
{
   // Texto de informações
   ObjectDelete(0, textoInfo);
   if(ObjectCreate(0, textoInfo, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, textoInfo, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, textoInfo, OBJPROP_XDISTANCE, 10); // Mais próximo da borda direita
      ObjectSetInteger(0, textoInfo, OBJPROP_YDISTANCE, 15); // Mais próximo do topo
      ObjectSetInteger(0, textoInfo, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, textoInfo, OBJPROP_FONT, "Consolas");
      ObjectSetInteger(0, textoInfo, OBJPROP_FONTSIZE, 6); // Fonte ainda menor
      ObjectSetInteger(0, textoInfo, OBJPROP_ANCHOR, ANCHOR_RIGHT_UPPER);
      ObjectSetString(0, textoInfo, OBJPROP_TEXT, "Carregando...");
   }
}

//+------------------------------------------------------------------+
void AtualizarTextoInfo()
{
   // Contar ordens abertas
   int totalOrdens = 0;
   int totalProtegidas = 0;
   double lucroAtualTotal = 0.0;
   double pipsTotal = 0.0;
   
   // Obter histórico do dia
   double lucroHistoricoDia = 0.0;
   int winsHistoricoDia = 0;
   int lossesHistoricoDia = 0;
   
   // Definir início do dia atual (00:00:00)
   MqlDateTime tempoAtual;
   TimeToStruct(TimeCurrent(), tempoAtual);
   datetime inicioDia = StringToTime(StringFormat("%04d.%02d.%02d 00:00:00", 
                                    tempoAtual.year, tempoAtual.mon, tempoAtual.day));
   
   // Selecionar histórico do dia
   if(HistorySelect(inicioDia, TimeCurrent()))
   {
      int totalDeals = HistoryDealsTotal();
      
      for(int i = 0; i < totalDeals; i++)
      {
         ulong dealTicket = HistoryDealGetTicket(i);
         if(dealTicket > 0)
         {
            // Verificar se é uma operação de fechamento
            if(HistoryDealGetInteger(dealTicket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
               // Verificar se é do símbolo atual
               if(HistoryDealGetString(dealTicket, DEAL_SYMBOL) == _Symbol)
               {
                  // Verificar se é uma ordem de qualquer versão do nosso EA
                  string dealComment = HistoryDealGetString(dealTicket, DEAL_COMMENT);
                  
                  // Considerar todos os prefixos possíveis (Robo SMT1 até SMT6)
                  bool isOurEA = false;
                  for(int p = 1; p <= 6; p++)
                  {
                     string prefixo = "RoboSMT" + IntegerToString(p) + "_";
                     if(StringFind(dealComment, prefixo) >= 0)
                     {
                        isOurEA = true;
                        break;
                     }
                  }
                  
                  if(isOurEA)
                  {
                     double profit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
                     lucroHistoricoDia += profit;
                     
                     if(profit > 0)
                        winsHistoricoDia++;
                     else if(profit < 0)
                        lossesHistoricoDia++;
                  }
               }
            }
         }
      }
   }
   
   // Contar ordens abertas e calcular lucro atual
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            totalOrdens++;
            
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double lucroAtual = PositionGetDouble(POSITION_PROFIT);
            lucroAtualTotal += lucroAtual;
            
            // Verificar se a posição está protegida
            for(int j = 0; j < total_protegidas; j++)
            {
               if(posicoes_protegidas[j].ticket == ticket && posicoes_protegidas[j].protegido)
               {
                  totalProtegidas++;
                  break;
               }
            }
            
            // Calcular lucro em pips
            double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
            double precoAtual = PositionGetDouble(POSITION_PRICE_CURRENT);
            double pipValue = _Point;
            
            // Ajustar para pares de moedas JPY
            if(StringFind(_Symbol, "JPY") >= 0)
               pipValue = 0.01;
            
            double lucroEmPips = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                                (precoAtual - precoAbertura) / pipValue :
                                (precoAbertura - precoAtual) / pipValue;

            pipsTotal += lucroEmPips;
         }
      }
   }
   
   // Criar texto de informações
   string texto = "";
   texto += "ROBO SMT5 v6.0\n";
   texto += "-------------------\n";
   texto += "Ordens: " + IntegerToString(totalOrdens) + "/" + IntegerToString(maxOrdens);
   if(totalProtegidas > 0)
      texto += " (" + IntegerToString(totalProtegidas) + " protegidas)";
   texto += "\n";
   
   texto += "Lucro Atual: $" + DoubleToString(lucroAtualTotal, 2);
   if(pipsTotal != 0)
      texto += " (" + DoubleToString(pipsTotal, 1) + " pips)";
   texto += "\n";
   
   texto += "-------------------\n";
   texto += "Hoje: $" + DoubleToString(lucroHistoricoDia, 2) + "\n";
   texto += "Wins: " + IntegerToString(winsHistoricoDia) + " | Losses: " + IntegerToString(lossesHistoricoDia) + "\n";
   
   if(winsHistoricoDia + lossesHistoricoDia > 0)
   {
      double winRate = (double)winsHistoricoDia / (winsHistoricoDia + lossesHistoricoDia) * 100.0;
      texto += "Win Rate: " + DoubleToString(winRate, 1) + "%\n";
   }
   
   texto += "-------------------\n";
   texto += "Total: $" + DoubleToString(lucroTotal, 2) + "\n";
   texto += "Greens: " + IntegerToString(greens) + " | Reds: " + IntegerToString(reds);
   
   // Atualizar o texto
   ObjectSetString(0, textoInfo, OBJPROP_TEXT, texto);
}

//+------------------------------------------------------------------+
void VerificarNovasPosicoes()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            
            // Verificar se já está no array
            bool encontrado = false;
            for(int j = 0; j < total_posicoes_abertas; j++)
            {
               if(posicoes_abertas[j].ticket == ticket)
               {
                  encontrado = true;
                  break;
               }
            }
            
            // Se não estiver no array, adicionar
            if(!encontrado)
            {
               // Adicionar ao array
               posicoes_abertas[total_posicoes_abertas].ticket = ticket;
               posicoes_abertas[total_posicoes_abertas].simbolo = _Symbol;
               posicoes_abertas[total_posicoes_abertas].timeAbertura = (datetime)PositionGetInteger(POSITION_TIME);
               posicoes_abertas[total_posicoes_abertas].precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
               posicoes_abertas[total_posicoes_abertas].lote = PositionGetDouble(POSITION_VOLUME);
               posicoes_abertas[total_posicoes_abertas].tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
               posicoes_abertas[total_posicoes_abertas].stopLoss = PositionGetDouble(POSITION_SL);
               posicoes_abertas[total_posicoes_abertas].takeProfit = PositionGetDouble(POSITION_TP);
               posicoes_abertas[total_posicoes_abertas].comentario = comentario;
               posicoes_abertas[total_posicoes_abertas].notificada = false;
               
               total_posicoes_abertas++;
               
               Print("Nova posição detectada: #", ticket, " - ", EnumToString(posicoes_abertas[total_posicoes_abertas-1].tipo));
               
               // Enviar notificação de abertura
               if(StringLen(webhookURL) > 0)
               {
                  string jsonData = CriarJsonPosicao(ticket, "ABERTA", 0.0, "");
                  if(jsonData != "")
                  {
                     EnviarNotificacaoPost(jsonData);
                     posicoes_abertas[total_posicoes_abertas-1].notificada = true;
                     Print("📤 Notificação de abertura enviada para posição #", ticket);
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void VerificarPosicoesFechadas()
{
   for(int i = 0; i < total_posicoes_abertas; i++)
   {
      ulong ticket = posicoes_abertas[i].ticket;
      
      // Verificar se a posição ainda está aberta
      if(!PositionSelectByTicket(ticket))
      {
         // Posição foi fechada
         Print("Posição fechada detectada: #", ticket);
         
         // Verificar no histórico para obter o lucro final
         if(HistorySelect(posicoes_abertas[i].timeAbertura, TimeCurrent()))
         {
            int totalDeals = HistoryDealsTotal();
            double lucroFinal = 0.0;
            string motivoFechamento = "Desconhecido";
            
            for(int j = 0; j < totalDeals; j++)
            {
               ulong dealTicket = HistoryDealGetTicket(j);
               if(dealTicket > 0)
               {
                  if(HistoryDealGetInteger(dealTicket, DEAL_POSITION_ID) == ticket &&
                     HistoryDealGetInteger(dealTicket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
                  {
                     lucroFinal = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
                     
                     // Determinar motivo do fechamento
                     if(HistoryDealGetInteger(dealTicket, DEAL_REASON) == DEAL_REASON_SL)
                        motivoFechamento = "Stop Loss";
                     else if(HistoryDealGetInteger(dealTicket, DEAL_REASON) == DEAL_REASON_TP)
                        motivoFechamento = "Take Profit";
                     else if(HistoryDealGetInteger(dealTicket, DEAL_REASON) == DEAL_REASON_CLIENT)
                        motivoFechamento = "Manual";
                     
                     break;
                  }
               }
            }
            
            // Enviar notificação de fechamento
            if(StringLen(webhookURL) > 0)
            {
               string jsonData = CriarJsonPosicao(ticket, "FECHADA", lucroFinal, motivoFechamento);
               if(jsonData != "")
               {
                  EnviarNotificacaoPost(jsonData);
                  Print("📤 Notificação de fechamento enviada para posição #", ticket, " - Lucro: $", DoubleToString(lucroFinal, 2));
               }
            }

            // Atualizar contadores
            if(lucroFinal > 0)
            {
               greens++;
               lucroTotal += lucroFinal;
            }
            else if(lucroFinal < 0)
            {
               reds++;
               lucroTotal += lucroFinal;
            }
         }

         // Remover do array
         for(int j = i; j < total_posicoes_abertas - 1; j++)
         {
            posicoes_abertas[j] = posicoes_abertas[j + 1];
         }
         total_posicoes_abertas--;
         i--; // Ajustar o índice para não pular a próxima posição
      }
   }
}

//+------------------------------------------------------------------+
void VerificarProtecaoLucro()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double lucroAtual = PositionGetDouble(POSITION_PROFIT);
               
               // Verificar se já está protegida
               bool jaProtegida = false;
               int indiceProtecao = -1;
               
               for(int j = 0; j < total_protegidas; j++)
               {
                  if(posicoes_protegidas[j].ticket == ticket)
                  {
                     jaProtegida = posicoes_protegidas[j].protegido;
                     indiceProtecao = j;
                     break;
                  }
               }
               
               // Se não estiver protegida e o lucro for maior que o valor de proteção
               if(!jaProtegida && lucroAtual >= lucroProtecao)
               {
                  // Obter dados da posição
                  double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
                  double stopLoss = PositionGetDouble(POSITION_SL);
                  ENUM_POSITION_TYPE tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                  
                  // Calcular novo stop loss no preço de entrada (breakeven)
                  double novoSL = precoAbertura;
                  
                  // Adicionar um pequeno lucro ao breakeven (1 pip)
                  if(tipo == POSITION_TYPE_BUY)
                     novoSL += _Point; // Adicionar 1 pip para compra
                  else
                     novoSL -= _Point; // Subtrair 1 pip para venda
                  
                  // Verificar se o novo SL é melhor que o atual
                  bool modificar = false;
                  
                  if(tipo == POSITION_TYPE_BUY)
                     modificar = (stopLoss < novoSL) || stopLoss == 0;
                  else
                     modificar = (stopLoss > novoSL) || stopLoss == 0;
                  
                  if(modificar)
                  {
                     // Modificar o stop loss
                     if(trade.PositionModify(ticket, novoSL, PositionGetDouble(POSITION_TP)))
                     {
                        Print("✓ Proteção de lucro ativada para #", ticket, " - Novo SL: ", novoSL);
                        
                        // Adicionar à lista de posições protegidas
                        if(indiceProtecao == -1)
                        {
                           posicoes_protegidas[total_protegidas].ticket = ticket;
                           posicoes_protegidas[total_protegidas].protegido = true;
                           posicoes_protegidas[total_protegidas].lucroQuandoProtegido = lucroAtual;
                           total_protegidas++;
                        }
                        else
                        {
                           posicoes_protegidas[indiceProtecao].protegido = true;
                           posicoes_protegidas[indiceProtecao].lucroQuandoProtegido = lucroAtual;
                        }
                     }
                     else
                     {
                        Print("✗ Erro ao ativar proteção de lucro: ", GetLastError());
                     }
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void VerificarFechamentoAutomatico()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double lucroAtual = PositionGetDouble(POSITION_PROFIT);
               
               // Se o lucro for maior que o valor de fechamento automático
               if(lucroAtual >= lucroFechamentoAutomatico)
               {
                  // Fechar a posição
                  if(trade.PositionClose(ticket))
                  {
                     Print("✓ Fechamento automático por lucro para #", ticket, " - Lucro: $", DoubleToString(lucroAtual, 2));
                  }
                  else
                  {
                     Print("✗ Erro ao fechar posição automaticamente: ", GetLastError());
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void VerificarTrailingStop()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               // Obter dados da posição
               double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
               double precoAtual = PositionGetDouble(POSITION_PRICE_CURRENT);
               double stopLoss = PositionGetDouble(POSITION_SL);
               ENUM_POSITION_TYPE tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
               
               // Calcular distância em pips
               double pipValue = _Point;
               
               // Ajustar para pares de moedas JPY
               if(StringFind(_Symbol, "JPY") >= 0)
                  pipValue = 0.01;
               
               double distanciaEmPips = 0;
               
               if(tipo == POSITION_TYPE_BUY)
                  distanciaEmPips = (precoAtual - precoAbertura) / pipValue;
               else
                  distanciaEmPips = (precoAbertura - precoAtual) / pipValue;
               
               // Verificar se atingiu o trailing start
               if(distanciaEmPips >= trailingStart)
               {
                  // Calcular novo stop loss
                  double novoSL = 0;
                  
                  if(tipo == POSITION_TYPE_BUY)
                     novoSL = precoAtual - (trailingStep * pipValue);
                  else
                     novoSL = precoAtual + (trailingStep * pipValue);
                  
                  // Verificar se o novo SL é melhor que o atual
                  bool modificar = false;
                  
                  if(tipo == POSITION_TYPE_BUY)
                     modificar = (stopLoss < novoSL) || stopLoss == 0;
                  else
                     modificar = (stopLoss > novoSL) || stopLoss == 0;
                  
                  if(modificar)
                  {
                     // Modificar o stop loss
                     if(trade.PositionModify(ticket, novoSL, PositionGetDouble(POSITION_TP)))
                     {
                        Print("✓ Trailing Stop ajustado para #", ticket, " - Novo SL: ", novoSL);
                     }
                     else
                     {
                        Print("✗ Erro ao ajustar Trailing Stop: ", GetLastError());
                     }
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void LimparPosicoesProtegidas()
{
   for(int i = 0; i < total_protegidas; i++)
   {
      // Verificar se a posição ainda está aberta
      if(!PositionSelectByTicket(posicoes_protegidas[i].ticket))
      {
         // Remover do
