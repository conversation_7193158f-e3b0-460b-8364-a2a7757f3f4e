#include <Trade\Trade.mqh>
#include <WinAPI\winapi.mqh>

// Renomear as variáveis de input para evitar conflitos
input double lote = 0.3;
input int inputStopLoss = 5;      // pips
input int inputTakeProfit = 5;    // pips
input int trailingStart = 2;  // pips - quando começar o trailing
input int trailingStep = 5;   // pips - distância do trailing stop
input int maxOrdens = 10;      // número máximo de ordens permitidas
input double lucroProtecao = 3.00; // valor em $ para ativar proteção de lucro
input double lucroFechamentoAutomatico = 10.00; // valor em $ para fechamento automático
input string webhookURL = "https://0a7055a29b9ca3023161ab35ff98e8eb.m.pipedream.net"; // URL do webhook

CTrade trade;
string prefixoComentario = "RoboSMT5_";  // Prefixo para identificar ordens deste EA

string botaoCompra = "btn_buy";
string botaoVenda = "btn_sell";
string botaoFechar = "btn_close_all";
string botaoFecharLucro = "btn_close_profit";
string textoInfo = "info_text";
string painelFundo = "painel_fundo";

// Variáveis para os objetos de linha
string prefixoLinhaTP = "tp_line_";
string prefixoLinhaSL = "sl_line_";
string prefixoLinhaEntrada = "entry_line_";

// Variáveis de controle para proteção de lucro
struct ProtecaoLucro
{
   ulong ticket;
   bool protegido;
   double lucroQuandoProtegido;
};

ProtecaoLucro posicoes_protegidas[100]; // Array para controlar posições protegidas
int total_protegidas = 0;

// Variáveis para controle de alertas de lucro
struct AlertaLucro
{
   ulong ticket;
   double ultimoLucroAlertado;
   datetime ultimoAlerta;
};

AlertaLucro alertas_lucro[100]; // Array para controlar alertas de lucro
int total_alertas = 0;

// Variáveis para o novo texto de informações
int greens = 0;  // Contador de operações com lucro
int reds = 0;    // Contador de operações com prejuízo
double lucroTotal = 0.0;  // Lucro total do dia

// Estrutura para armazenar dados de posições abertas
struct PosicaoAberta
{
   ulong ticket;
   datetime timeAbertura;
   double precoAbertura;
   double loteUsado;
   ENUM_POSITION_TYPE tipo;
   double stopLossInicial;
   double takeProfitInicial;
   string comentario;
   bool notificada;
};

PosicaoAberta posicoes_abertas[100];
int total_posicoes_abertas = 0;

// Variáveis para o histórico do dia
int winsHistoricoDia = 0;
int lossesHistoricoDia = 0;
double lucroHistoricoDia = 0.0;

//+------------------------------------------------------------------+
int OnInit()
{
   // Inicializar array de proteção
   for(int i = 0; i < 100; i++)
   {
      posicoes_protegidas[i].ticket = 0;
      posicoes_protegidas[i].protegido = false;
      posicoes_protegidas[i].lucroQuandoProtegido = 0.0;
   }
   total_protegidas = 0;
   
   // Inicializar array de alertas
   for(int i = 0; i < 100; i++)
   {
      alertas_lucro[i].ticket = 0;
      alertas_lucro[i].ultimoLucroAlertado = 0.0;
      alertas_lucro[i].ultimoAlerta = 0;
   }
   total_alertas = 0;
   
   // Inicializar array de posições abertas
   for(int i = 0; i < 100; i++)
   {
      posicoes_abertas[i].ticket = 0;
      posicoes_abertas[i].notificada = false;
   }
   total_posicoes_abertas = 0;
   
   // Resetar contadores de operações
   greens = 0;
   reds = 0;
   lucroTotal = 0.0;
   
   // Habilitar eventos de clique no gráfico
   ChartSetInteger(0, CHART_EVENT_OBJECT_CREATE, true);
   ChartSetInteger(0, CHART_EVENT_OBJECT_DELETE, true);
   ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);
   ChartSetInteger(0, CHART_EVENT_OBJECT_CLICK, true);

   CriarBotoes();
   CriarTextoInfo();
   ChartRedraw();

   Print("=== ROBO SMT5 v6.0 INICIADO ===");
   Print("Configurações:");
   Print("- Lote: ", lote);
   Print("- Stop Loss: ", inputStopLoss, " pips");
   Print("- Take Profit: ", inputTakeProfit, " pips");
   Print("- Proteção de Lucro: $", lucroProtecao);
   Print("- Fechamento Automático: $", lucroFechamentoAutomatico);
   Print("- Trailing Start: ", trailingStart, " pips");
   Print("- Max Ordens: ", maxOrdens);
   Print("- Webhook URL: ", webhookURL);
   Print("========================");

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ObjectDelete(0, botaoCompra);
   ObjectDelete(0, botaoVenda);
   ObjectDelete(0, botaoFechar);
   ObjectDelete(0, botaoFecharLucro);
   ObjectDelete(0, textoInfo);
   
   // Remover todas as linhas de SL, TP e entrada
   RemoverTodasLinhasSLTP();
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
void OnTick()
{
   // Verificar novas posições abertas
   VerificarNovasPosicoes();
   
   // Verificar posições fechadas
   VerificarPosicoesFechadas();
   
   // Limpar posições protegidas que já foram fechadas
   LimparPosicoesProtegidas();
   
   // Limpar alertas de lucro para posições fechadas
   LimparAlertasLucro();

   // Verificar fechamento automático por lucro
   VerificarFechamentoAutomatico();

   // Verificar proteção de lucro primeiro (mais importante)
   VerificarProtecaoLucro();
   
   // Verificar alertas de lucro
   VerificarAlertasLucro();

   // Verificar se alguma ordem atingiu o take profit ou stop loss
   VerificarFechamentoOrdens();

   // Gerenciar trailing stop para todas as posições abertas
   GerenciarTrailingStopTodasPosicoes();

   // Atualizar linhas de SL, TP e entrada no gráfico
   AtualizarLinhasSLTP();

   // Atualizar texto de informações
   AtualizarTextoInfo();
}

//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // Verificar se é um clique em um objeto
   if(id == CHARTEVENT_OBJECT_CLICK)
   {
      Print("Objeto clicado: ", sparam);
      
      if(sparam == botaoCompra)
      {
         Print("Botão COMPRA clicado");
         // Verificar se já atingiu o número máximo de ordens
         if(ContarOrdens() < maxOrdens)
         {
            Print("Executando compra...");
            ExecutarCompra();
            // Resetar o estado do botão
            ObjectSetInteger(0, botaoCompra, OBJPROP_STATE, false);
         }
         else
         {
            Print("Número máximo de ordens atingido: ", maxOrdens);
            // Resetar o estado do botão
            ObjectSetInteger(0, botaoCompra, OBJPROP_STATE, false);
         }
         ChartRedraw();
      }
      else if(sparam == botaoVenda)
      {
         Print("Botão VENDA clicado");
         // Verificar se já atingiu o número máximo de ordens
         if(ContarOrdens() < maxOrdens)
         {
            Print("Executando venda...");
            ExecutarVenda();
            // Resetar o estado do botão
            ObjectSetInteger(0, botaoVenda, OBJPROP_STATE, false);
         }
         else
         {
            Print("Número máximo de ordens atingido: ", maxOrdens);
            // Resetar o estado do botão
            ObjectSetInteger(0, botaoVenda, OBJPROP_STATE, false);
         }
         ChartRedraw();
      }
      else if(sparam == botaoFechar)
      {
         Print("Botão FECHAR clicado");
         // Fechar todas as posições abertas
         FecharTodasPosicoes();
         // Resetar o estado do botão
         ObjectSetInteger(0, botaoFechar, OBJPROP_STATE, false);
         ChartRedraw();
      }
      else if(sparam == botaoFecharLucro)
      {
         Print("Botão FECHAR LUCRO clicado");
         // Fechar todas as posições com lucro positivo
         FecharPosicoesComLucro();
         // Resetar o estado do botão
         ObjectSetInteger(0, botaoFecharLucro, OBJPROP_STATE, false);
         ChartRedraw();
      }
   }
}

//+------------------------------------------------------------------+
// Função para enviar notificação POST
bool EnviarNotificacaoPost(string jsonData)
{
   string headers = "Content-Type: application/json\r\n";
   
   char data[];
   char result[];
   string result_headers;
   
   // Converter string para array de bytes
   int data_size = StringToCharArray(jsonData, data, 0, WHOLE_ARRAY, CP_UTF8) - 1;
   
   Print("📤 Tentando enviar para webhook: ", webhookURL);
   Print("📤 Dados: ", jsonData);
   
   // Fazer a requisição HTTP POST
   int timeout = 5000; // 5 segundos
   int res = WebRequest("POST", webhookURL, headers, timeout, data, result, result_headers);
   
   if(res == 200)
   {
      Print("✅ Notificação enviada com sucesso!");
      return true;
   }
   else
   {
      Print("❌ Erro ao enviar notificação. Código: ", res);
      Print("❌ Descrição do erro: ", GetLastError());
      
      // Verificar erros comuns
      if(res == -1)
      {
         Print("⚠️ ERRO CRÍTICO: Verifique se a URL está na lista de URLs permitidas em Ferramentas > Opções > Conselheiros Especialistas");
         MessageBox("A URL do webhook não está autorizada. Vá para Ferramentas > Opções > Conselheiros Especialistas e adicione: " + webhookURL, "Erro de Webhook", MB_ICONERROR);
      }
      else if(res == 0)
      {
         Print("⚠️ Conexão falhou. Verifique sua internet ou firewall");
      }
      
      Print("Headers de resposta: ", result_headers);
      
      // Converter resultado para string para debug
      string resultStr = CharArrayToString(result, 0, WHOLE_ARRAY);
      if(StringLen(resultStr) > 0)
         Print("Resposta: ", resultStr);
      
      return false;
   }
}

//+------------------------------------------------------------------+
// Função para criar JSON com dados da posição fechada
string CriarJsonPosicao(ulong ticket, string evento, double lucroFinal = 0.0, string motivoFechamento = "")
{
   if(evento != "FECHADA")
      return "";
   
   // Dados básicos
   string symbol = _Symbol;
   datetime timeAtual = TimeCurrent();
   string timeString = TimeToString(timeAtual, TIME_DATE|TIME_SECONDS);
   
   // Dados da posição fechada
   double precoAbertura = 0;
   double precoFechamento = 0;
   double loteUsado = 0;
   string tipoOperacao = "";
   double stopLoss = 0;
   double takeProfit = 0;
   datetime timeAbertura = 0;
   string comentario = "";
   double lucroAtual = 0;
   
   // Buscar dados da posição no array de posições abertas
   for(int i = 0; i < total_posicoes_abertas; i++)
   {
      if(posicoes_abertas[i].ticket == ticket)
      {
         precoAbertura = posicoes_abertas[i].precoAbertura;
         loteUsado = posicoes_abertas[i].loteUsado;
         tipoOperacao = (posicoes_abertas[i].tipo == POSITION_TYPE_BUY) ? "BUY" : "SELL";
         stopLoss = posicoes_abertas[i].stopLossInicial;
         takeProfit = posicoes_abertas[i].takeProfitInicial;
         timeAbertura = posicoes_abertas[i].timeAbertura;
         comentario = posicoes_abertas[i].comentario;
         break;
      }
   }
   
   // Se não encontrou no array, buscar no histórico
   if(precoAbertura == 0 && HistorySelectByPosition(ticket))
   {
      int totalDeals = HistoryDealsTotal();
      
      for(int j = 0; j < totalDeals; j++)
      {
         ulong dealTicket = HistoryDealGetTicket(j);
         if(HistoryDealGetInteger(dealTicket, DEAL_POSITION_ID) == ticket)
         {
            if(HistoryDealGetInteger(dealTicket, DEAL_ENTRY) == DEAL_ENTRY_IN)
            {
               precoAbertura = HistoryDealGetDouble(dealTicket, DEAL_PRICE);
               loteUsado = HistoryDealGetDouble(dealTicket, DEAL_VOLUME);
               tipoOperacao = (HistoryDealGetInteger(dealTicket, DEAL_TYPE) == DEAL_TYPE_BUY) ? "BUY" : "SELL";
               comentario = HistoryDealGetString(dealTicket, DEAL_COMMENT);
               timeAbertura = (datetime)HistoryDealGetInteger(dealTicket, DEAL_TIME);
               break;
            }
         }
      }
      
      // Buscar o deal de fechamento para obter o preço de fechamento
      for(int j = totalDeals - 1; j >= 0; j--)
      {
         ulong dealTicket = HistoryDealGetTicket(j);
         if(HistoryDealGetInteger(dealTicket, DEAL_POSITION_ID) == ticket)
         {
            if(HistoryDealGetInteger(dealTicket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
               precoFechamento = HistoryDealGetDouble(dealTicket, DEAL_PRICE);
               break;
            }
         }
      }
   }
   
   // Se ainda não temos preço de fechamento, usar o preço atual
   if(precoFechamento == 0)
   {
      precoFechamento = (tipoOperacao == "BUY") ? 
                       SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                       SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   }
   
   lucroAtual = lucroFinal;
   
   // Calcular pips
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   double pipValue = point;
   if(digits == 5 || digits == 3) pipValue = point * 10;
   
   double pips = 0;
   if(tipoOperacao == "BUY")
      pips = (precoFechamento - precoAbertura) / pipValue;
   else
      pips = (precoAbertura - precoFechamento) / pipValue;
   
   // Duração da operação
   int duracaoSegundos = (int)(timeAtual - timeAbertura);
   int duracaoMinutos = duracaoSegundos / 60;
   
   // Obter histórico do dia
   double lucroHistoricoDia = 0.0;
   int totalOperacoesDia = 0;
   int winsHistoricoDia = 0;
   int lossesHistoricoDia = 0;
   
   // Definir início do dia atual (00:00:00)
   MqlDateTime tempoAtual;
   TimeToStruct(timeAtual, tempoAtual);
   datetime inicioDia = StringToTime(StringFormat("%04d.%02d.%02d 00:00:00", 
                                    tempoAtual.year, tempoAtual.mon, tempoAtual.day));
   
   // Selecionar histórico do dia
   if(HistorySelect(inicioDia, timeAtual))
   {
      int totalDeals = HistoryDealsTotal();
      
      Print("Total de deals no histórico do dia: ", totalDeals);
      
      for(int i = 0; i < totalDeals; i++)
      {
         ulong dealTicket = HistoryDealGetTicket(i);
         if(dealTicket > 0)
         {
            // Verificar se é uma operação de fechamento
            if(HistoryDealGetInteger(dealTicket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
            {
               // Verificar se é do símbolo atual
               if(HistoryDealGetString(dealTicket, DEAL_SYMBOL) == _Symbol)
               {
                  // Verificar se é uma ordem de qualquer versão do nosso EA
                  string dealComment = HistoryDealGetString(dealTicket, DEAL_COMMENT);
                  
                  // Considerar todos os prefixos possíveis (Robo SMT1 até SMT6)
                  bool isOurEA = false;
                  for(int p = 1; p <= 6; p++)
                  {
                     string prefixo = "RoboSMT" + IntegerToString(p) + "_";
                     if(StringFind(dealComment, prefixo) >= 0)
                     {
                        isOurEA = true;
                        break;
                     }
                  }
                  
                  if(isOurEA)
                  {
                     double dealProfit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
                     lucroHistoricoDia += dealProfit;
                     totalOperacoesDia++;
                     
                     if(dealProfit > 0)
                        winsHistoricoDia++;
                     else if(dealProfit < 0)
                        lossesHistoricoDia++;
                        
                     Print("Deal encontrado: #", dealTicket, 
                           ", Lucro: ", DoubleToString(dealProfit, 2), 
                           ", Comentário: ", dealComment);
                  }
               }
            }
         }
      }
   }
   
   Print("Histórico do dia - Total operações: ", totalOperacoesDia, 
         ", Wins: ", winsHistoricoDia, 
         ", Losses: ", lossesHistoricoDia, 
         ", Lucro total: ", DoubleToString(lucroHistoricoDia, 2));
   
   // Criar JSON com informações detalhadas para métricas
   string json = "{";
   json += "\"evento\":\"" + evento + "\",";
   json += "\"timestamp\":\"" + timeString + "\",";
   json += "\"ticket\":" + IntegerToString(ticket) + ",";
   json += "\"symbol\":\"" + symbol + "\",";
   json += "\"tipo\":\"" + tipoOperacao + "\",";
   json += "\"lote\":" + DoubleToString(loteUsado, 2) + ",";
   json += "\"precoAbertura\":" + DoubleToString(precoAbertura, _Digits) + ",";
   json += "\"precoFechamento\":" + DoubleToString(precoFechamento, _Digits) + ",";
   json += "\"stopLoss\":" + DoubleToString(stopLoss, _Digits) + ",";
   json += "\"takeProfit\":" + DoubleToString(takeProfit, _Digits) + ",";
   json += "\"lucro\":" + DoubleToString(lucroAtual, 2) + ",";
   json += "\"pips\":" + DoubleToString(pips, 1) + ",";
   json += "\"timeAbertura\":\"" + TimeToString(timeAbertura, TIME_DATE|TIME_SECONDS) + "\",";
   json += "\"timeFechamento\":\"" + timeString + "\",";
   json += "\"duracaoMinutos\":" + IntegerToString(duracaoMinutos) + ",";
   json += "\"duracaoSegundos\":" + IntegerToString(duracaoSegundos) + ",";
   json += "\"comentario\":\"" + comentario + "\",";
   json += "\"motivoFechamento\":\"" + motivoFechamento + "\",";
   json += "\"resultado\":\"" + (lucroFinal > 0 ? "WIN" : (lucroFinal < 0 ? "LOSS" : "ZERO")) + "\",";
   json += "\"greensTotal\":" + IntegerToString(greens) + ",";
   json += "\"redsTotal\":" + IntegerToString(reds) + ",";
   json += "\"lucroTotalDia\":" + DoubleToString(lucroTotal, 2) + ",";
   json += "\"historicoDia\":{";
   json += "\"totalOperacoes\":" + IntegerToString(totalOperacoesDia) + ",";
   json += "\"wins\":" + IntegerToString(winsHistoricoDia) + ",";
   json += "\"losses\":" + IntegerToString(lossesHistoricoDia) + ",";
   json += "\"lucroTotal\":" + DoubleToString(lucroHistoricoDia, 2) + ",";
   json += "\"taxaAcerto\":" + DoubleToString(totalOperacoesDia > 0 ? (double)winsHistoricoDia/totalOperacoesDia*100 : 0, 1) + ""; 
   json += "},";
   json += "\"configuracoes\":{";
   json += "\"stopLossPips\":" + IntegerToString(inputStopLoss) + ",";
   json += "\"takeProfitPips\":" + IntegerToString(inputTakeProfit) + ",";
   json += "\"lucroProtecao\":" + DoubleToString(lucroProtecao, 2) + ",";
   json += "\"lucroFechamentoAuto\":" + DoubleToString(lucroFechamentoAutomatico, 2) + ",";
   json += "\"trailingStart\":" + IntegerToString(trailingStart) + ",";
   json += "\"trailingStep\":" + IntegerToString(trailingStep);
   json += "}";
   json += "}";
   
   return json;
}

//+------------------------------------------------------------------+
// Função para verificar novas posições abertas
void VerificarNovasPosicoes()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);

            // Verificar se já está no array
            bool jaRegistrada = false;
            for(int j = 0; j < total_posicoes_abertas; j++)
            {
               if(posicoes_abertas[j].ticket == ticket)
               {
                  jaRegistrada = true;
                  break;
               }
            }

            // Se não está registrada, adicionar (mas não enviar notificação)
            if(!jaRegistrada && total_posicoes_abertas < 100)
            {
               posicoes_abertas[total_posicoes_abertas].ticket = ticket;
               posicoes_abertas[total_posicoes_abertas].timeAbertura = (datetime)PositionGetInteger(POSITION_TIME);
               posicoes_abertas[total_posicoes_abertas].precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
               posicoes_abertas[total_posicoes_abertas].loteUsado = PositionGetDouble(POSITION_VOLUME);
               posicoes_abertas[total_posicoes_abertas].tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
               posicoes_abertas[total_posicoes_abertas].stopLossInicial = PositionGetDouble(POSITION_SL);
               posicoes_abertas[total_posicoes_abertas].takeProfitInicial = PositionGetDouble(POSITION_TP);
               posicoes_abertas[total_posicoes_abertas].comentario = comentario;
               posicoes_abertas[total_posicoes_abertas].notificada = false;
               total_posicoes_abertas++;
               
               Print("✓ Nova posição registrada - Ticket #", ticket);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
// Função para verificar posições fechadas
void VerificarPosicoesFechadas()
{
   for(int i = total_posicoes_abertas - 1; i >= 0; i--)
   {
      ulong ticket = posicoes_abertas[i].ticket;

      // Verificar se a posição ainda existe
      if(!PositionSelectByTicket(ticket))
      {
         // Posição foi fechada - buscar no histórico para obter dados finais
         if(HistorySelectByPosition(ticket))
         {
            int totalDeals = HistoryDealsTotal();
            double lucroFinal = 0;
            string motivoFechamento = "MANUAL";

            // Buscar o deal de fechamento
            for(int j = totalDeals - 1; j >= 0; j--)
            {
               ulong dealTicket = HistoryDealGetTicket(j);
               if(HistoryDealGetInteger(dealTicket, DEAL_POSITION_ID) == ticket)
               {
                  if(HistoryDealGetInteger(dealTicket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
                  {
                     lucroFinal = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);

                     // Determinar motivo do fechamento
                     string dealComment = HistoryDealGetString(dealTicket, DEAL_COMMENT);
                     if(StringFind(dealComment, "sl") >= 0 || StringFind(dealComment, "stop") >= 0)
                        motivoFechamento = "STOP_LOSS";
                     else if(StringFind(dealComment, "tp") >= 0 || StringFind(dealComment, "take") >= 0)
                        motivoFechamento = "TAKE_PROFIT";
                     else if(StringFind(dealComment, "trailing") >= 0)
                        motivoFechamento = "TRAILING_STOP";

                     break;
                  }
               }
            }

            // Enviar notificação de fechamento
            string jsonData = CriarJsonPosicao(ticket, "FECHADA", lucroFinal, motivoFechamento);
            if(jsonData != "")
            {
               EnviarNotificacaoPost(jsonData);
               Print("📤 Notificação de fechamento enviada para posição #", ticket, " - Lucro: $", DoubleToString(lucroFinal, 2));
            }

            // Atualizar contadores
            if(lucroFinal > 0)
            {
               greens++;
               lucroTotal += lucroFinal;
            }
            else if(lucroFinal < 0)
            {
               reds++;
               lucroTotal += lucroFinal;
            }
         }

         // Remover do array
         for(int j = i; j < total_posicoes_abertas - 1; j++)
         {
            posicoes_abertas[j] = posicoes_abertas[j + 1];
         }
         total_posicoes_abertas--;
      }
   }
}

//+------------------------------------------------------------------+
int ContarOrdens()
{
   int contador = 0;

   // Contar posições abertas
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         // Verificar se a posição foi aberta por este EA
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
            contador++;
      }
   }

   return contador;
}

//+------------------------------------------------------------------+
void ExecutarCompra()
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

   double pipValue = point;
   if(digits == 5 || digits == 3)
      pipValue = point * 10;

   double sl = ask - (inputStopLoss * pipValue);
   double tp = ask + (inputTakeProfit * pipValue);

   string comentario = prefixoComentario + "Compra_" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS);

   Print("Executando COMPRA:");
   Print("Ask: ", ask, " SL: ", sl, " TP: ", tp);

   if(trade.Buy(lote, _Symbol, ask, sl, tp, comentario))
   {
      Print("✓ COMPRA executada com sucesso! Ticket: ", trade.ResultOrder());
      // Atualizar linhas de SL, TP e entrada imediatamente após abrir a posição
      AtualizarLinhasSLTP();
   }
   else
   {
      Print("✗ Erro na COMPRA: ", trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
void ExecutarVenda()
{
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

   double pipValue = point;
   if(digits == 5 || digits == 3)
      pipValue = point * 10;

   double sl = bid + (inputStopLoss * pipValue);
   double tp = bid - (inputTakeProfit * pipValue);

   string comentario = prefixoComentario + "Venda_" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS);

   Print("Executando VENDA:");
   Print("Bid: ", bid, " SL: ", sl, " TP: ", tp);

   if(trade.Sell(lote, _Symbol, bid, sl, tp, comentario))
   {
      Print("✓ VENDA executada com sucesso! Ticket: ", trade.ResultOrder());
      // Atualizar linhas de SL, TP e entrada imediatamente após abrir a posição
      AtualizarLinhasSLTP();
   }
   else
   {
      Print("✗ Erro na VENDA: ", trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
void GerenciarTrailingStopTodasPosicoes()
{
   int total = PositionsTotal();

   for(int i = 0; i < total; i++)
   {
      // Selecionar posição pelo índice
      string simbolo = PositionGetSymbol(i);

      // Verificar se é uma posição no símbolo atual
      if(simbolo == _Symbol)
      {
         // Verificar se a posição foi aberta por este EA
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            // Aplicar trailing stop para esta posição
            GerenciarTrailingStopPosicao(PositionGetInteger(POSITION_TICKET));
         }
      }
   }
}

//+------------------------------------------------------------------+
void GerenciarTrailingStopPosicao(ulong ticket)
{
   // Selecionar a posição pelo ticket
   if(!PositionSelectByTicket(ticket)) return;

   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

   double pipValue = point;
   if(digits == 5 || digits == 3)
      pipValue = point * 10;

   double precoAtual = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                       SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                       SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
   double stopAtual = PositionGetDouble(POSITION_SL);
   double lucroAtual = PositionGetDouble(POSITION_PROFIT);

   // Verificar se o stop loss está definido
   bool stopDefinido = (stopAtual > 0);

   // Verificar se a posição está protegida
   bool posicaoProtegida = false;
   double lucroQuandoProtegido = 0;
   for(int i = 0; i < total_protegidas; i++)
   {
      if(posicoes_protegidas[i].ticket == ticket && posicoes_protegidas[i].protegido)
      {
         posicaoProtegida = true;
         lucroQuandoProtegido = posicoes_protegidas[i].lucroQuandoProtegido;
         break;
      }
   }

   // Se a posição já está protegida, aplicar trailing stop mais agressivo
   if(posicaoProtegida)
   {
      double novoStop = 0;

      if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
      {
         // Posição de COMPRA
         // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
         novoStop = precoAtual - (trailingStep * pipValue);

         // Só move o stop se for melhor que o atual
         if(stopAtual == 0 || novoStop > stopAtual + (pipValue * 0.1))
         {
            if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
            {
               Print("Trailing Stop atualizado - Ticket #", ticket,
                     ": Novo SL = ", novoStop,
                     " (Lucro atual: $", DoubleToString(lucroAtual, 2),
                     ", Lucro quando protegido: $", DoubleToString(lucroQuandoProtegido, 2), ")");

               // Atualizar linhas de SL, TP e entrada após modificar o stop loss
               AtualizarLinhasSLTP();
            }
         }
      }
      else
      {
         // Posição de VENDA
         // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
         novoStop = precoAtual + (trailingStep * pipValue);

         // Só move o stop se for melhor que o atual
         if(stopAtual == 0 || novoStop < stopAtual - (pipValue * 0.1))
         {
            if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
            {
               Print("Trailing Stop atualizado - Ticket #", ticket,
                     ": Novo SL = ", novoStop,
                     " (Lucro atual: $", DoubleToString(lucroAtual, 2),
                     ", Lucro quando protegido: $", DoubleToString(lucroQuandoProtegido, 2), ")");

               // Atualizar linhas de SL, TP e entrada após modificar o stop loss
               AtualizarLinhasSLTP();
            }
         }
      }
   }
   // Se não está protegida e o lucro atingiu o valor de proteção, ativar proteção
   else if(lucroAtual >= lucroProtecao)
   {
      AplicarProtecaoLucro(ticket, lucroAtual);
   }
   // Se não está protegida, aplicar o trailing normal quando atingir trailingStart pips
   else
   {
      if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
      {
         // Posição de COMPRA
         double lucroEmPips = (precoAtual - precoAbertura) / pipValue;

         // Verificar se o lucro atingiu o nível para ativar o trailing (trailingStart pips)
         if(lucroEmPips >= trailingStart)
         {
            // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
            double novoStop = precoAtual - (trailingStep * pipValue);

            // Só move o stop se for melhor que o atual ou se não houver stop definido
            if(!stopDefinido || novoStop > stopAtual + (pipValue * 0.1))
            {
               if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
               {
                  Print("Trailing Stop ativado - Ticket #", ticket, ": Novo SL = ", novoStop, " (Lucro: ", DoubleToString(lucroEmPips, 1), " pips)");
                  // Atualizar linhas de SL, TP e entrada após modificar o stop loss
                  AtualizarLinhasSLTP();
               }
            }
         }
      }
      else
      {
         // Posição de VENDA
         double lucroEmPips = (precoAbertura - precoAtual) / pipValue;

         // Verificar se o lucro atingiu o nível para ativar o trailing (trailingStart pips)
         if(lucroEmPips >= trailingStart)
         {
            // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
            double novoStop = precoAtual + (trailingStep * pipValue);

            // Só move o stop se for melhor que o atual ou se não houver stop definido
            if(!stopDefinido || novoStop < stopAtual - (pipValue * 0.1))
            {
               if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
               {
                  Print("Trailing Stop ativado - Ticket #", ticket, ": Novo SL = ", novoStop, " (Lucro: ", DoubleToString(lucroEmPips, 1), " pips)");
                  // Atualizar linhas de SL, TP e entrada após modificar o stop loss
                  AtualizarLinhasSLTP();
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void VerificarFechamentoOrdens()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double precoAtual = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                                  SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                  SymbolInfoDouble(_Symbol, SYMBOL_ASK);

               double stopLossAtual = PositionGetDouble(POSITION_SL);
               double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
               double lucroAtual = PositionGetDouble(POSITION_PROFIT);

               // Calcular lucro em pips
               double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
               int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
               double pipValue = point;
               if(digits == 5 || digits == 3) pipValue = point * 10;

               double lucroEmPips = 0;

               // Verificar se a posição está protegida e se o lucro caiu significativamente
               for(int j = 0; j < total_protegidas; j++)
               {
                  if(posicoes_protegidas[j].ticket == ticket && posicoes_protegidas[j].protegido)
                  {
                     double lucroQuandoProtegido = posicoes_protegidas[j].lucroQuandoProtegido;
                     double lucroMaximo = lucroQuandoProtegido;

                     // Se o lucro atual caiu para menos de 70% do lucro máximo registrado
                     if(lucroAtual < lucroMaximo * 0.7 && lucroAtual > 0)
                     {
                        string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";
                        string mensagem = "FECHANDO POSIÇÃO PROTEGIDA! #" + IntegerToString(ticket) +
                                         " (" + tipoOrdem + ") - Lucro caiu de $" +
                                         DoubleToString(lucroMaximo, 2) + " para $" +
                                         DoubleToString(lucroAtual, 2);

                        Alert(mensagem);
                        Print(mensagem);

                        if(trade.PositionClose(ticket))
                        {
                           Print("✓ Posição fechada por proteção de lucro - Ticket #", ticket);
                        }
                        else
                        {
                           Print("✗ Erro ao fechar posição por proteção: ", GetLastError());
                        }

                        break;
                     }

                     // Atualizar o lucro máximo registrado se o lucro atual for maior
                     if(lucroAtual > lucroMaximo)
                     {
                        posicoes_protegidas[j].lucroQuandoProtegido = lucroAtual;
                        Print("Lucro máximo atualizado para posição #", ticket, ": $", DoubleToString(lucroAtual, 2));
                     }
                  }
               }

               // Verificar se atingiu o stop loss
               if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
               {
                  lucroEmPips = (precoAtual - precoAbertura) / pipValue;

                  // Verificar se atingiu o stop loss
                  if(stopLossAtual > 0 && precoAtual <= stopLossAtual)
                  {
                     if(trade.PositionClose(ticket))
                     {
                        Print("✓ Posição fechada por Stop Loss - COMPRA, Ticket #", ticket,
                              ", Perda: ", DoubleToString(-lucroEmPips, 1), " pips");
                     }
                     else
                     {
                        Print("✗ Erro ao fechar posição por Stop Loss: ", GetLastError());
                     }
                  }
               }
               else // POSITION_TYPE_SELL
               {
                  lucroEmPips = (precoAbertura - precoAtual) / pipValue;

                  // Verificar se atingiu o stop loss
                  if(stopLossAtual > 0 && precoAtual >= stopLossAtual)
                  {
                     if(trade.PositionClose(ticket))
                     {
                        Print("✓ Posição fechada por Stop Loss - VENDA, Ticket #", ticket,
                              ", Perda: ", DoubleToString(-lucroEmPips, 1), " pips");
                     }
                     else
                     {
                        Print("✗ Erro ao fechar posição por Stop Loss: ", GetLastError());
                     }
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void VerificarAlertasLucro()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double lucroAtual = PositionGetDouble(POSITION_PROFIT);

            // Verificar se o lucro é positivo (maior que 0)
            if(lucroAtual > 0)
            {
               // Verificar se já temos um alerta para este ticket
               int indiceAlerta = -1;
               for(int j = 0; j < total_alertas; j++)
               {
                  if(alertas_lucro[j].ticket == ticket)
                  {
                     indiceAlerta = j;
                     break;
                  }
               }

               // Se não temos alerta ou o lucro aumentou significativamente
               if(indiceAlerta == -1 || lucroAtual >= alertas_lucro[indiceAlerta].ultimoLucroAlertado + 1.0)
               {
                  // Verificar se passaram pelo menos 30 segundos desde o último alerta
                  bool podeAlertar = true;
                  if(indiceAlerta != -1)
                  {
                     if(TimeCurrent() - alertas_lucro[indiceAlerta].ultimoAlerta < 30)
                     {
                        podeAlertar = false;
                     }
                  }

                  if(podeAlertar)
                  {
                     string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";
                     string mensagem = "ALERTA: Posição #" + IntegerToString(ticket) +
                                      " (" + tipoOrdem + ") com LUCRO de $" +
                                      DoubleToString(lucroAtual, 2);

                     // Usar Alert para garantir que o usuário veja a notificação
                     Alert(mensagem);
                     Print(mensagem);

                     // Atualizar ou adicionar ao array de alertas
                     if(indiceAlerta == -1)
                     {
                        // Adicionar novo alerta
                        if(total_alertas < 100)
                        {
                           alertas_lucro[total_alertas].ticket = ticket;
                           alertas_lucro[total_alertas].ultimoLucroAlertado = lucroAtual;
                           alertas_lucro[total_alertas].ultimoAlerta = TimeCurrent();
                           total_alertas++;
                        }
                     }
                     else
                     {
                        // Atualizar alerta existente
                        alertas_lucro[indiceAlerta].ultimoLucroAlertado = lucroAtual;
                        alertas_lucro[indiceAlerta].ultimoAlerta = TimeCurrent();
                     }
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void LimparAlertasLucro()
{
   // Remover alertas para posições que já foram fechadas
   for(int i = total_alertas - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(alertas_lucro[i].ticket))
      {
         // Posição foi fechada, remover do array
         for(int j = i; j < total_alertas - 1; j++)
         {
            alertas_lucro[j] = alertas_lucro[j + 1];
         }
         total_alertas--;
      }
   }
}

//+------------------------------------------------------------------+
void VerificarProtecaoLucro()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double lucroAtual = PositionGetDouble(POSITION_PROFIT);

            // Verificar se precisa aplicar proteção
            if(lucroAtual >= lucroProtecao && !PosicaoJaProtegida(ticket))
            {
               string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";
               string mensagem = "PROTEÇÃO: Posição #" + IntegerToString(ticket) +
                               " (" + tipoOrdem + ") atingiu $" +
                               DoubleToString(lucroAtual, 2) + " - Aplicando proteção...";

               Alert(mensagem);
               Print(mensagem);

               AplicarProtecaoLucro(ticket, lucroAtual);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
bool PosicaoJaProtegida(ulong ticket)
{
   for(int i = 0; i < total_protegidas; i++)
   {
      if(posicoes_protegidas[i].ticket == ticket && posicoes_protegidas[i].protegido)
      {
         return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
void AplicarProtecaoLucro(ulong ticket, double lucroAtual)
{
   if(!PositionSelectByTicket(ticket)) return;

   double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
   double stopAtual = PositionGetDouble(POSITION_SL);
   ENUM_POSITION_TYPE tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
   double precoAtual = (tipo == POSITION_TYPE_BUY) ?
                       SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                       SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   // Calcular valores de pip
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   double pipValue = point;
   if(digits == 5 || digits == 3) pipValue = point * 10;

   // Calcular novo stop loss para acompanhar o preço (trailing stop)
   double novoStop = 0;
   if(tipo == POSITION_TYPE_BUY)
   {
      // Para compras, mover o stop para trailingStep pips abaixo do preço atual
      novoStop = precoAtual - (trailingStep * pipValue);
   }
   else
   {
      // Para vendas, mover o stop para trailingStep pips acima do preço atual
      novoStop = precoAtual + (trailingStep * pipValue);
   }

   // Verificar se vale a pena atualizar o stop loss
   bool deveAtualizar = false;

   if(tipo == POSITION_TYPE_BUY)
   {
      // Para compra, o novo stop deve ser maior que o atual
      if(stopAtual == 0 || novoStop > stopAtual)
      {
         deveAtualizar = true;
      }
   }
   else
   {
      // Para venda, o novo stop deve ser menor que o atual
      if(stopAtual == 0 || novoStop < stopAtual)
      {
         deveAtualizar = true;
      }
   }

   if(deveAtualizar)
   {
      if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
      {
         // Registrar posição como protegida
         bool posicaoJaRegistrada = false;
         for(int i = 0; i < total_protegidas; i++)
         {
            if(posicoes_protegidas[i].ticket == ticket)
            {
               posicoes_protegidas[i].protegido = true;
               posicoes_protegidas[i].lucroQuandoProtegido = lucroAtual;
               posicaoJaRegistrada = true;
               break;
            }
         }

         if(!posicaoJaRegistrada && total_protegidas < 100)
         {
            posicoes_protegidas[total_protegidas].ticket = ticket;
            posicoes_protegidas[total_protegidas].protegido = true;
            posicoes_protegidas[total_protegidas].lucroQuandoProtegido = lucroAtual;
            total_protegidas++;
         }

         string tipoStr = (tipo == POSITION_TYPE_BUY) ? "COMPRA" : "VENDA";
         string mensagem = "PROTEÇÃO ATIVADA! Posição #" + IntegerToString(ticket) +
                          " (" + tipoStr + ") - Lucro: $" + DoubleToString(lucroAtual, 2) +
                          " - Stop movido para: " + DoubleToString(novoStop, _Digits) +
                          " (trailing " + IntegerToString(trailingStep) + " pips)";

         Alert(mensagem);
         Print(mensagem);

         // Atualizar gráfico
         AtualizarLinhasSLTP();
      }
      else
      {
         Print("❌ Erro ao aplicar proteção para ticket #", ticket, ": ", GetLastError());
      }
   }
}

//+------------------------------------------------------------------+
void VerificarFechamentoAutomatico()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double lucroAtual = PositionGetDouble(POSITION_PROFIT);

               // Verificar se o lucro atingiu o valor para fechamento automático
               if(lucroAtual >= lucroFechamentoAutomatico)
               {
                  string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";
                  string mensagem = "FECHAMENTO AUTOMÁTICO! Posição #" + IntegerToString(ticket) +
                                   " (" + tipoOrdem + ") atingiu lucro de $" +
                                   DoubleToString(lucroAtual, 2) + " - Fechando...";

                  Alert(mensagem);
                  Print(mensagem);

                  if(trade.PositionClose(ticket))
                  {
                     Print("✓ Posição fechada por atingir lucro alvo - Ticket #", ticket);
                  }
                  else
                  {
                     Print("✗ Erro ao fechar posição por lucro alvo: ", GetLastError());
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void CriarBotoes()
{
   // Definir tamanhos e posições para botões menores e lado a lado
   int larguraBotao = 60;  // Botões mais estreitos
   int alturaBotao = 20;   // Botões mais baixos
   int espacamento = 5;    // Menor espaçamento entre botões
   int distanciaBase = 40; // Distância da base da tela (mais acima)
   int distanciaLateral = 10; // Distância da lateral esquerda
   
   // Botão COMPRA (primeiro da esquerda)
   ObjectDelete(0, botaoCompra);
   if(ObjectCreate(0, botaoCompra, OBJ_BUTTON, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoCompra, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, botaoCompra, OBJPROP_XDISTANCE, distanciaLateral);
      ObjectSetInteger(0, botaoCompra, OBJPROP_YDISTANCE, distanciaBase);
      ObjectSetInteger(0, botaoCompra, OBJPROP_XSIZE, larguraBotao);
      ObjectSetInteger(0, botaoCompra, OBJPROP_YSIZE, alturaBotao);
      ObjectSetInteger(0, botaoCompra, OBJPROP_BGCOLOR, clrGreen);
      ObjectSetInteger(0, botaoCompra, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoCompra, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoCompra, OBJPROP_TEXT, "");
      ObjectSetInteger(0, botaoCompra, OBJPROP_FONTSIZE, 7);
   }

   // Botão VENDA (segundo da esquerda)
   ObjectDelete(0, botaoVenda);
   if(ObjectCreate(0, botaoVenda, OBJ_BUTTON, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoVenda, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, botaoVenda, OBJPROP_XDISTANCE, distanciaLateral + larguraBotao + espacamento);
      ObjectSetInteger(0, botaoVenda, OBJPROP_YDISTANCE, distanciaBase);
      ObjectSetInteger(0, botaoVenda, OBJPROP_XSIZE, larguraBotao);
      ObjectSetInteger(0, botaoVenda, OBJPROP_YSIZE, alturaBotao);
      ObjectSetInteger(0, botaoVenda, OBJPROP_BGCOLOR, clrRed);
      ObjectSetInteger(0, botaoVenda, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoVenda, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoVenda, OBJPROP_TEXT, "");
      ObjectSetInteger(0, botaoVenda, OBJPROP_FONTSIZE, 7);
   }

   // Botão FECHAR (terceiro da esquerda)
   ObjectDelete(0, botaoFechar);
   if(ObjectCreate(0, botaoFechar, OBJ_BUTTON, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoFechar, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, botaoFechar, OBJPROP_XDISTANCE, distanciaLateral + (larguraBotao + espacamento) * 2);
      ObjectSetInteger(0, botaoFechar, OBJPROP_YDISTANCE, distanciaBase);
      ObjectSetInteger(0, botaoFechar, OBJPROP_XSIZE, larguraBotao);
      ObjectSetInteger(0, botaoFechar, OBJPROP_YSIZE, alturaBotao);
      ObjectSetInteger(0, botaoFechar, OBJPROP_BGCOLOR, clrDarkOrange);
      ObjectSetInteger(0, botaoFechar, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoFechar, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoFechar, OBJPROP_TEXT, "");
      ObjectSetInteger(0, botaoFechar, OBJPROP_FONTSIZE, 7);
   }
   
   // Botão FECHAR LUCRO (quarto da esquerda)
   ObjectDelete(0, botaoFecharLucro);
   if(ObjectCreate(0, botaoFecharLucro, OBJ_BUTTON, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_XDISTANCE, distanciaLateral + (larguraBotao + espacamento) * 3);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_YDISTANCE, distanciaBase);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_XSIZE, larguraBotao);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_YSIZE, alturaBotao);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_BGCOLOR, clrDodgerBlue);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoFecharLucro, OBJPROP_TEXT, "");
      ObjectSetInteger(0, botaoFecharLucro, OBJPROP_FONTSIZE, 7);
   }
}

//+------------------------------------------------------------------+
void CriarTextoInfo()
{
   // Texto de informações
   ObjectDelete(0, textoInfo);
   if(ObjectCreate(0, textoInfo, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, textoInfo, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, textoInfo, OBJPROP_XDISTANCE, 10); // Mais próximo da borda direita
      ObjectSetInteger(0, textoInfo, OBJPROP_YDISTANCE, 15); // Mais próximo do topo
      ObjectSetInteger(0, textoInfo, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, textoInfo, OBJPROP_FONT, "Consolas");
      ObjectSetInteger(0, textoInfo, OBJPROP_FONTSIZE, 6); // Fonte ainda menor
      ObjectSetInteger(0, textoInfo, OBJPROP_ANCHOR, ANCHOR_RIGHT_UPPER);
      ObjectSetString(0, textoInfo, OBJPROP_TEXT, "Carregando...");
   }
}

//+------------------------------------------------------------------+
void AtualizarTextoInfo()
{
   int totalOrdens = ContarOrdens();
   int totalProtegidas = ContarPosicoesProtegidas();
   double lucroAtualTotal = 0.0;
   double pipsTotal = 0.0;
   
   // Calcular lucro atual e pips totais
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            double profit = PositionGetDouble(POSITION_PROFIT);
            lucroAtualTotal += profit;
            
            // Calcular pips
            double precoEntrada = PositionGetDouble(POSITION_PRICE_OPEN);
            double precoAtual = PositionGetDouble(POSITION_PRICE_CURRENT);
            ENUM_POSITION_TYPE tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            double pipValue = _Point * 10; // Valor de 1 pip
            double pips = 0;
            
            if(tipo == POSITION_TYPE_BUY)
               pips = (precoAtual - precoEntrada) / pipValue;
            else
               pips = (precoEntrada - precoAtual) / pipValue;
               
            pipsTotal += pips;
         }
      }
   }

   // Texto formatado com cada informação em uma linha separada e sem caracteres especiais
   string texto = "ROBO SMT6 v6.0\n" +
                  "Ordens: " + IntegerToString(totalOrdens) + "\n" +
                  "Protegidas: " + IntegerToString(totalProtegidas) + "\n" +
                  "Lucro: $" + DoubleToString(lucroAtualTotal, 2) + "\n" +
                  "Pips: " + DoubleToString(pipsTotal, 1) + "\n" +
                  "G:" + IntegerToString(greens) + " R:" + IntegerToString(reds) + "\n" +
                  "Lucro: $" + DoubleToString(lucroTotal, 2) + "\n" +
                  "W:" + IntegerToString(winsHistoricoDia) + " L:" + IntegerToString(lossesHistoricoDia) + "\n" +
                  "Hist: $" + DoubleToString(lucroHistoricoDia, 2) + "\n" +
                  "Lote:" + DoubleToString(lote, 2) + " SL:" + IntegerToString(inputStopLoss) + "\n" +
                  "TP:" + IntegerToString(inputTakeProfit) + " Prot:$" + DoubleToString(lucroProtecao, 2);

   ObjectSetString(0, textoInfo, OBJPROP_TEXT, texto);
}

//+------------------------------------------------------------------+
int ContarPosicoesProtegidas()
{
   int contador = 0;
   for(int i = 0; i < total_protegidas; i++)
   {
      // Verificar se a posição ainda existe
      if(PositionSelectByTicket(posicoes_protegidas[i].ticket))
      {
         contador++;
      }
   }
   return contador;
}

//+------------------------------------------------------------------+
void LimparPosicoesProtegidas()
{
   // Remover posições que já foram fechadas do array de proteção
   for(int i = total_protegidas - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(posicoes_protegidas[i].ticket))
      {
         // Posição foi fechada, remover do array
         for(int j = i; j < total_protegidas - 1; j++)
         {
            posicoes_protegidas[j] = posicoes_protegidas[j + 1];
         }
         total_protegidas--;
      }
   }
}

//+------------------------------------------------------------------+
void RemoverTodasLinhasSLTP()
{
   // Remover todas as linhas de SL, TP e entrada
   for(int i = ObjectsTotal(0, 0, OBJ_HLINE) - 1; i >= 0; i--)
   {
      string nome = ObjectName(0, i, 0, OBJ_HLINE);
      if(StringFind(nome, prefixoLinhaSL) == 0 ||
         StringFind(nome, prefixoLinhaTP) == 0 ||
         StringFind(nome, prefixoLinhaEntrada) == 0)
      {
         ObjectDelete(0, nome);
      }
   }
}

//+------------------------------------------------------------------+
void AtualizarLinhasSLTP()
{
   // Primeiro, remover todas as linhas existentes
   RemoverTodasLinhasSLTP();

   // Depois, criar novas linhas para cada posição aberta
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double stopLoss = PositionGetDouble(POSITION_SL);
               double takeProfit = PositionGetDouble(POSITION_TP);
               double precoEntrada = PositionGetDouble(POSITION_PRICE_OPEN);
               ENUM_POSITION_TYPE tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

               // Criar linha de preço de entrada
               string nomeLinhaEntrada = prefixoLinhaEntrada + IntegerToString(ticket);
               if(ObjectCreate(0, nomeLinhaEntrada, OBJ_HLINE, 0, 0, precoEntrada))
               {
                  ObjectSetInteger(0, nomeLinhaEntrada, OBJPROP_COLOR, clrYellow);
                  ObjectSetInteger(0, nomeLinhaEntrada, OBJPROP_STYLE, STYLE_SOLID);
                  ObjectSetInteger(0, nomeLinhaEntrada, OBJPROP_WIDTH, 1);
                  ObjectSetString(0, nomeLinhaEntrada, OBJPROP_TOOLTIP, "Entrada #" + IntegerToString(ticket) +
                                 " (" + (tipo == POSITION_TYPE_BUY ? "COMPRA" : "VENDA") + ")");
               }

               // Criar linha de Stop Loss
               if(stopLoss > 0)
               {
                  string nomeLinhaSL = prefixoLinhaSL + IntegerToString(ticket);
                  if(ObjectCreate(0, nomeLinhaSL, OBJ_HLINE, 0, 0, stopLoss))
                  {
                     ObjectSetInteger(0, nomeLinhaSL, OBJPROP_COLOR, clrRed);
                     ObjectSetInteger(0, nomeLinhaSL, OBJPROP_STYLE, STYLE_DASH);
                     ObjectSetInteger(0, nomeLinhaSL, OBJPROP_WIDTH, 1);
                     ObjectSetString(0, nomeLinhaSL, OBJPROP_TOOLTIP, "Stop Loss #" + IntegerToString(ticket) +
                                    " (" + (tipo == POSITION_TYPE_BUY ? "COMPRA" : "VENDA") + ")");
                  }
               }

               // Criar linha de Take Profit
               if(takeProfit > 0)
               {
                  string nomeLinhaTP = prefixoLinhaTP + IntegerToString(ticket);
                  if(ObjectCreate(0, nomeLinhaTP, OBJ_HLINE, 0, 0, takeProfit))
                  {
                     ObjectSetInteger(0, nomeLinhaTP, OBJPROP_COLOR, clrGreen);
                     ObjectSetInteger(0, nomeLinhaTP, OBJPROP_STYLE, STYLE_DASH);
                     ObjectSetInteger(0, nomeLinhaTP, OBJPROP_WIDTH, 1);
                     ObjectSetString(0, nomeLinhaTP, OBJPROP_TOOLTIP, "Take Profit #" + IntegerToString(ticket) +
                                    " (" + (tipo == POSITION_TYPE_BUY ? "COMPRA" : "VENDA") + ")");
                  }
               }
            }
         }
      }
   }

   ChartRedraw();
}

//+------------------------------------------------------------------+
void FecharTodasPosicoes()
{
   int total = PositionsTotal();

   // Precisamos iterar de trás para frente porque o PositionsTotal() muda quando fechamos posições
   for(int i = total - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double lucroAtual = PositionGetDouble(POSITION_PROFIT);

            if(trade.PositionClose(ticket))
            {
               Print("✓ Posição fechada manualmente - Ticket #", ticket);
               
               // Não precisamos enviar notificação aqui, pois a função VerificarPosicoesFechadas
               // detectará automaticamente que a posição foi fechada e enviará a notificação
            }
            else
            {
               Print("✗ Erro ao fechar posição: ", GetLastError());
            }
         }
      }
   }

   // Atualizar o gráfico após fechar todas as posições
   AtualizarLinhasSLTP();
   AtualizarTextoInfo();
}

//+------------------------------------------------------------------+
void FecharPosicoesComLucro()
{
   int total = PositionsTotal();
   int fechadas = 0;
   double lucroTotal = 0.0;
   
   // Precisamos iterar de trás para frente porque o PositionsTotal() muda quando fechamos posições
   for(int i = total - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double lucroAtual = PositionGetDouble(POSITION_PROFIT);
            
            // Verificar se a posição está com lucro positivo
            if(lucroAtual > 0)
            {
               string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";
               
               if(trade.PositionClose(ticket))
               {
                  fechadas++;
                  lucroTotal += lucroAtual;
                  Print("Posicao fechada por estar com lucro - Ticket #", ticket, 
                        " (", tipoOrdem, ") - Lucro: $", DoubleToString(lucroAtual, 2));
               }
               else
               {
                  Print("Erro ao fechar posicao com lucro: ", GetLastError());
               }
            }
         }
      }
   }
   
   if(fechadas > 0)
   {
      string mensagem = "FECHAMENTO DE LUCRO! Fechadas " + IntegerToString(fechadas) + 
                       " posicoes com lucro total de $" + DoubleToString(lucroTotal, 2);
      Alert(mensagem);
      Print(mensagem);
   }
   else
   {
      Print("Nenhuma posicao com lucro para fechar");
   }
   
   // Atualizar o gráfico após fechar as posições
   AtualizarLinhasSLTP();
   AtualizarTextoInfo();
}
